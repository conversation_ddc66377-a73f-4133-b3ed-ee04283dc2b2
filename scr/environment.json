{"id": "97ad0eae-b40a-42cb-bc4b-a1482fdc550e", "name": "Pre-Prod", "values": [{"key": "vethubURL", "value": "https://api-vh-preprod.vethub.net", "enabled": true}, {"key": "recipient", "value": "B37CF562-E88C-4908-9D31-BF447FFB3026", "enabled": true}, {"key": "vendorpassword", "value": "51A8B7A8-C38D-4C48-8325-B056312857C3", "enabled": true}, {"key": "userid", "value": "r<PERSON><PERSON><PERSON><PERSON><PERSON>", "enabled": true}, {"key": "userpassword", "value": "2cdb5977", "enabled": true}, {"key": "policyNumber", "value": "", "enabled": true}, {"key": "Notificationurl", "value": "", "enabled": true}, {"key": "cosURL", "value": "https://api-cos-preprod.petsure.com.au/", "enabled": true}, {"key": "ClaimApiUrl", "value": "https://api-claims-preprod.petsure.com.au/", "enabled": true}, {"key": "StorageApiUrl", "value": "https://api-storage-preprod.petsure.com.au/", "enabled": true}, {"key": "dapiURL", "value": "http://api-digital-preprod.petsure.com.au/", "enabled": true}, {"key": "CatchApiUrl", "value": "https://catch-api-preprod.petsure.com.au", "enabled": true}, {"key": "queryid", "value": "", "enabled": true}, {"key": "OpenVPMS_userid", "value": "openvpmsuat", "enabled": true}, {"key": "OPENVPMS_userpassword", "value": "032716b5", "enabled": true}, {"key": "OPENVPMS_vendorpassword", "value": "29FE9380-6BEC-4EE6-8EB3-29DCD03ED0C2", "enabled": true}, {"key": "InsuranceProvider_userid", "value": "PETSURE", "enabled": true}, {"key": "InsuranceProvider_password", "value": "PETSURE", "enabled": true}, {"key": "ProviderMemberId", "value": "B37CF562-E88C-4908-9D31-BF447FFB3026", "enabled": true}, {"key": "SecurityApiUrl", "value": "https://api-security-uat.petsure.com.au/", "enabled": true}, {"key": "SecurityClientId", "value": "24fd2dd2-8f9a-4112-a9bf-fa6769fcd19a", "enabled": true}, {"key": "SecurityAppKey", "value": "cwuKyP1Fvg1Rlta3jqlpCiPzQ54FRcC1fVWXBcTTpNU=", "enabled": true}, {"key": "conversationId", "value": "112a3ab7-ac7b-44f5-bff9-193554f4934e", "enabled": true}, {"key": "Attach_id", "value": "be79315a-7d9d-47c0-a011-2c402553ad3d", "enabled": true}, {"key": "userID2", "value": "MANILA VET", "enabled": true}, {"key": "userID2PW", "value": "TESTPASSWORD", "enabled": true}, {"key": "RXworks-userid", "value": "CHATSWOOD VET", "enabled": true}, {"key": "RXworks-userpassword", "value": "TESTPASSWORD", "enabled": true}, {"key": "date", "value": "lap0403g", "enabled": true}, {"key": "conversationID", "value": "00319c79-85f4-4c22-ae50-1640c2c1d480", "enabled": true}, {"key": "attachmentID", "value": "12fba7ad-2023-45fc-8cba-4df8ec985d02", "enabled": true}, {"key": "userIDEzyVet", "value": "rfeb1fzu5y", "enabled": true}, {"key": "userPasswordEzyVet", "value": "8468e7f2", "enabled": true}, {"key": "authToken", "value": "2fd53f882bda1409c1f443484f52097c610c2f07", "enabled": true}, {"key": "claimRefNumber", "value": "CPREPROD2019112917", "enabled": true}, {"key": "userIDOpenVPMS", "value": "rbnov11160", "enabled": true}, {"key": "userPasswordOpenVPMS", "value": "c538ae75", "enabled": true}, {"key": "locationIDOpenVPMSParent", "value": "VPID111601", "enabled": true}], "_postman_variable_scope": "environment", "_postman_exported_at": "2025-07-02T03:22:28.145Z", "_postman_exported_using": "Postman/11.49.0"}