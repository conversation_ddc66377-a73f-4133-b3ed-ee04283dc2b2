import json
import requests
import os

def run_postman_requests(collection_path, environment_path):
    """
    Runs a series of requests defined in a Postman collection,
    using environment variables.

    Args:
        collection_path (str): The file path for the Postman collection JSON.
        environment_path (str): The file path for the Postman environment JSON.
    """
    try:
        with open(environment_path, 'r') as f:
            env_data = json.load(f)
        
        env_vars = {item['key']: item['value'] for item in env_data.get('values', [])}
        base_url = env_vars.get('vethubURL')

        if not base_url:
            print("❌ 'vethubURL' not found in the environment file.")
            return

        with open(collection_path, 'r') as f:
            collection_data = json.load(f)

        # --- 1. Get ConversationID ---
        print("🚀 Starting the process...")
        print("\n--- Step 1: Getting Conversation ID ---")
        try:
            new_conversation_url = f"{base_url}/api/claim/NewConversationId"
            response_conv = requests.get(new_conversation_url)
            response_conv.raise_for_status()  # Raises an HTTPError for bad responses (4xx or 5xx)
            conversation_id = response_conv.json().get('ConversationId')
            if conversation_id:
                print(f"✅ Successfully retrieved ConversationID: {conversation_id}")
            else:
                print("❌ Could not find 'ConversationId' in the response.")
                return
        except requests.exceptions.RequestException as e:
            print(f"Error during Get ConversationID request: {e}")
            return

        # --- 2. Get AttachmentID ---
        print("\n--- Step 2: Getting Attachment ID ---")
        try:
            new_attachment_url = f"{base_url}/api/claim/{conversation_id}/Attachment/NewConversationId"
            response_attach_id = requests.get(new_attachment_url)
            response_attach_id.raise_for_status()
            attachment_id = response_attach_id.json().get('AttachmentId') # Assuming the key is 'AttachmentId'
            if attachment_id:
                 print(f"✅ Successfully retrieved AttachmentID: {attachment_id}")
            else:
                print("❌ Could not find 'AttachmentId' in the response.")
                return
        except requests.exceptions.RequestException as e:
            print(f"Error during Get AttachmentID request: {e}")
            return


        # --- 3. Submit Attachment (Clinical) ---
        print("\n--- Step 3: Submitting Attachment ---")
        # You will need to specify the path to your clinical attachment file
        attachment_file_path = '/home/<USER>/repos/sophia_junior/test_print.pdf' # <--- CHANGE THIS

        if not os.path.exists(attachment_file_path):
            print(f"❌ Attachment file not found at: {attachment_file_path}")
            return
            
        submit_attachment_url = f"{base_url}/api/claim/attachment/{attachment_id}"
        
        try:
            with open(attachment_file_path, 'rb') as f:
                files = {'file': (os.path.basename(attachment_file_path), f)}
                response_submit_attach = requests.post(submit_attachment_url, files=files)
                response_submit_attach.raise_for_status()
            print(f"✅ Attachment submitted successfully. Status Code: {response_submit_attach.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"Error during Submit Attachment request: {e}")
            return
        except FileNotFoundError:
            print(f"❌ Attachment file not found at path: {attachment_file_path}")
            return


        # --- 4. Submit Claim ---
        print("\n--- Step 4: Submitting Claim ---")
        submit_claim_url = f"{base_url}/api/claim/{conversation_id}"
        
        # You need to provide the JSON body for the claim submission
        # This is an example, modify it to match your actual claim data
        claim_data = {
  "Identification": {
    "OwnerID": "45652",
    "PracticeID": "petsure",
    "PracticeClaimRef": "1545",
    "RequestGap": "Y"
  },
  "InfoFromPolicyHolder": {
    "PolicyDetails": {
      "PolicyNumber": "WP00003731S",
      "PolicyholderName": "Stella Seater",
      "Address": "5 Laurie Court, SKYE, VIC",
      "Postcode": "3977",
      "EmailAddress": "<EMAIL>",
      "PreferredContactBy": "Email",
      "InsuranceCompany": "JSON Compnay",
      "ExpiryDate": "2020-09-23"
    },
    "AnimalDetails": {
      "Name": "Holly",
      "Species": "Canine",
      "Breed": "Rottweiler",
      "DateOfBirth": "2014-09-23",
      "Gender": "Female",
      "Colour": "Black & Tan",
      "MicrochipNumber": ""
    },
    "Conditions": [ {
        "Description": "Torn Nail (different nail from last time)",
        "DateFirstNoticed": "2016-12-03"
      },
      {
        "Description": "Snake Bite",
        "DateFirstNoticed": "2017-12-03"
      }
    ],
    "Financial": {
      "PayVet": "true",
      "PayClaimTo": "vet"
    }
  },
  "InfoFromVet": {
    "Miscellaneous": {
      "AnimalRegistered": "2015-04-04",
      "LastVaccination": "2016-11-11",
      "MicrochipNumber": "956000003172356",
      "ClaimHandler": {
        "Name": "Tonia Brajcich  Cert IV Veterinary Nursing",
        "Email": "<EMAIL>"
      }
    },
    "Vet": {
      "VetSurname": "Morgan Woodforde BVMS BSc ",
      "VetForenames": "Dr",
      "PracticeName": "REMTest",
      "PracticeAddress": "REMTest",
      "PracticePhone": "9277 7488",
      "PracticePostcode": "2069",
      "RegistrationNumber": "420",
    },
    "AnimalClinicalHistory": [
      {
        "Date": "2016-12-03",
        "Time": "15:08 PM",
        "EnteredBy": "Dr Morgan Woodforde BVMS BSc ",
        "TextEntry": "Reason: Appt: Torn Nail    History: Noticed has torn nail today, does not seem as bad as last time.  Metacam intermittently, no other concerns today.    Examination:   BAR lovely dog  MM pink ,moist, CRT<2, teeth excellent  Left front 4th digit broken at end of quick  HR normal no murmur, chest sounds clear    Treatment: trimmed nail in tx, no bleeding from base    Assessment:   1. Left fore 4th nail injury    Plan:   1. Metacam as required for joints  2. If nail becomes infected can dispense antibiotics, not required at this stage      Vital Signs        Weight: 34.2;         "
      }
    ],
    "Conditions": [
      {
        "DiagnosisOrSigns": "Torn Nail (different nail from last time)123",
        "ClaimContinuation": "0",
    "ConditionCode": "Code1223",
        "Started": "2016-12-03",
        "DeathOrEuthanasia": "false",
        "OngoingCondition": "false",
        "TreatmentDates": {
          "DateFrom": "2016-12-03",
          "DateTo": "2016-12-03"
        },
        "Financial": {
          "TotalExVAT": "214.7727",
          "VAT": "11.4773",
          "TotalIncVat": "126.2700",
          "TotalDiscount": "57.59",
          "InvoiceItems": [
            {
              "Description": "Sick Patient Revisit Short",
              "Type": "Examinations",
              "AmountExVAT": "29.0909",
              "InvoiceNumber": "768677",
              "Date": "2016-12-03"
            },
            {
              "Description": "Metacam 100mls Liquid",
              "Type": "Medications-Oral",
              "AmountExVAT": "85.6818",
              "InvoiceNumber": "768677",
              "Date": "2016-12-03"
            }
          ],
          "Invoices": [
            {
              "Invoice": {
                "InvoiceNumber": "test_invoicenumber",
                "Date": "2015-12-03",
                "TotalExVAT": "30.02",
                "TotalDiscountExVAT": "125.58",
                "VAT": "12.23",
                "TotalIncVat": "158",
                "Items": [
                  {
                    "Item": {
                      "ItemCode": "test_itemcode",
                      "Sequence": "test_seq1",
                      "Description": "test_desc",
                      "Type": "test_type",
                      "AmountExVAT": "5.59",
                      "DiscountExVAT": "58.65",
                      "VAT": "98.98",
                      "Quantity": "45.59",
                      "TotalIncVAT": "98.59"
                    }
                  }
                ]
              }
            }
          ]
        },
        "Remarks": "I hereby agree that:    1.     All material submitted in connection with this claim is true, accurate and complete in all material respects and no relevant information has been withheld, including without limitation, any discounts which may have been applied;    2.     The veterinary practice has obtained the authority of the policyholder to submit this claim on their behalf; and    3.     The veterinary practice has received payment in full from the policyholder in respect of any service or treatment connected with this claim.    4.     PetSure and its personnel can request veterinary practice  records connected with this claim for audit purposes.    Dr Morgan Woodforde BVMS BSc on 09/12/2016"
      },
    {
        "DiagnosisOrSigns": "Snake Bite",
        "ClaimContinuation": "0",
        "ConsultationNotes": "This is a test",
    "ConditionCode": "Code12345",
        "Started": "2016-12-03",
        "DeathOrEuthanasia": "false",
        "OngoingCondition": "false",
        "TreatmentDates": {
          "DateFrom": "2016-12-03",
          "DateTo": "2016-12-03"
        },
        "Financial": {
          "TotalExVAT": "114.7727",
          "VAT": "11.4773",
          "TotalIncVat": "126.2500",
          "TotalDiscount": "57.59",
          "InvoiceItems": [
            {
              "Description": "Sick Patient Rev",
              "Type": "Examinations",
              "AmountExVAT": "29.0909",
              "InvoiceNumber": "768677",
              "Date": "2016-12-03"
            },
            {
              "Description": "Metacam 100m",
              "Type": "Medications-Oral",
              "AmountExVAT": "85.6818",
              "InvoiceNumber": "768677",
              "Date": "2016-12-03"
            }
          ],
          "Invoices": [
            {
              "Invoice": {
                "InvoiceNumber": "test_invoicenumbe23",
                "Date": "2015-12-03",
                "TotalExVAT": "30.02",
                "TotalDiscountExVAT": "125.58",
                "VAT": "12.23",
                "TotalIncVat": "158",
                "Items": [
                  {
                    "Item": {
                      "ItemCode": "test_itemco34",
                      "Sequence": "test_seq1",
                      "Description": "test_desc",
                      "Type": "test_type",
                      "AmountExVAT": "5.59",
                      "DiscountExVAT": "58.65",
                      "VAT": "98.98",
                      "Quantity": "45.59",
                      "TotalIncVAT": "98.59"
                    }
                  }
                ]
              },
              "ServiceProviderNumber": "CT0000040"
            }
          ]
        },
        "Remarks": "I hereby agree that:    1.     All material submitted in connection with this claim is true, accurate and complete in all material respects and no relevant information has been withheld, including without limitation, any discounts which may have been applied;    2.     The veterinary practice has obtained the authority of the policyholder to submit this claim on their behalf; and    3.     The veterinary practice has received payment in full from the policyholder in respect of any service or treatment connected with this claim.    4.     PetSure and its personnel can request veterinary practice  records connected with this claim for audit purposes.    Dr Morgan Woodforde BVMS BSc on 09/12/2016"
      }
    ],
    "Referrals": [
      {
        "ReferralType": "General",
        "ReferralReason": "Left hind lameness / Hip dysplasia",
        "ReferralPractice": {
          "Name": "Rivergum Referral Centre",
          "Address": "PO Box 344, Willeton, WA, 6155",
          "Phone": "08 9259-6344"
        },
        "ReferralDate": "2015-07-26"
      }
    ]
  }
}

        try:
            response_submit_claim = requests.post(submit_claim_url, json=claim_data)
            response_submit_claim.raise_for_status()
            print(f"✅ Claim submitted successfully! Status Code: {response_submit_claim.status_code}")
            print("Response Body:")
            print(response_submit_claim.json())
        except requests.exceptions.RequestException as e:
            print(f"Error during Submit Claim request: {e}")
            return
            
        print("\n🎉 All steps completed successfully!")


    except FileNotFoundError as e:
        print(f"❌ Error: {e}. Please make sure the file paths are correct.")
    except json.JSONDecodeError:
        print("❌ Error decoding JSON. Please check the format of your collection and environment files.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    # --- Configuration ---
    # Update these paths if your files are in a different directory
    collection_filename = '/home/<USER>/repos/sophia_junior/scr/environment.json'
    environment_filename = '/home/<USER>/repos/sophia_junior/scr/Smoke Testing Copy.postman_collection.json'

    run_postman_requests(collection_filename, environment_filename)