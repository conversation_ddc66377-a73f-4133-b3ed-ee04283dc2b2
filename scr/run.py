import json
import requests
import os
import pandas as pd
import time
from datetime import datetime

def run_postman_requests(environment_path, excel_file_path=None):
    """
    Runs a series of VetHub API requests for claim submission,
    using environment variables from a Postman environment file.

    Args:
        environment_path (str): The file path for the Postman environment JSON.
        excel_file_path (str, optional): The file path for the Excel file containing product names.
    """
    try:
        with open(environment_path, 'r') as f:
            env_data = json.load(f)
        
        env_vars = {item['key']: item['value'] for item in env_data.get('values', [])}
        base_url = env_vars.get('vethubURL')

        # Debug info (comment out for production)
        # print(f"Debug - Environment variables loaded: {len(env_vars)} variables")
        # print(f"Debug - Base URL: {base_url}")
        # print(f"Debug - UserID: {env_vars.get('userIDEzyVet', 'NOT_FOUND')}")
        # print(f"Debug - Date: {env_vars.get('date', 'NOT_FOUND')}")

        if not base_url:
            print("❌ 'vethubURL' not found in the environment file.")
            return

        # Read product data from Excel file if provided
        excel_data = []
        if excel_file_path:
            try:
                print(f"📊 Reading Excel file: {excel_file_path}")
                df = pd.read_excel(excel_file_path)
                if 'Product Name' in df.columns:
                    # Convert DataFrame to list of dictionaries for easier processing
                    excel_data = df.to_dict('records')
                    print(f"✅ Found {len(excel_data)} products in Excel file")
                    print(f"Available columns: {list(df.columns)}")
                    print(f"First few products: {[row['Product Name'] for row in excel_data[:3]]}")
                else:
                    print("❌ 'Product Name' column not found in Excel file")
                    print(f"Available columns: {list(df.columns)}")
                    return
            except Exception as e:
                print(f"❌ Error reading Excel file: {e}")
                return
        else:
            print("ℹ️ No Excel file provided, using default product descriptions")

        # Initialize output data list
        output_data = []

        # Generate invoice items based on Excel data or use defaults
        def generate_invoice_items(excel_data, max_items=None):
            if excel_data:
                # Use all data if max_items is None, otherwise limit to max_items
                limited_data = excel_data if max_items is None else excel_data[:max_items]
                # Create invoice items from Excel data
                invoice_items = []
                for i, row in enumerate(limited_data):
                    # Get values from Excel or use defaults
                    description = row.get('Product Name', f'Product {i+1}')
                    item_type = row.get('Category', 'Medications-Oral')  # Use Category from Excel or default
                    amount = row.get('Price', 29.09 + i * 10)

                    # Validate data before adding
                    if description and description.strip():  # Ensure description is not empty
                        item = {
                            "Description": description.strip(),
                            "Type": item_type if item_type else "Medications-Oral",  # Ensure Type is not empty
                            "AmountExVAT": f"{float(amount):.4f}",
                            "InvoiceNumber": "768677",
                            "Date": "2016-12-03"
                        }
                        invoice_items.append(item)
                return invoice_items
            else:
                # Default invoice items
                return [
                    {
                        "Description": "Sick Patient Revisit Short",
                        "Type": "Examinations",
                        "AmountExVAT": "29.0909",
                        "InvoiceNumber": "768677",
                        "Date": "2016-12-03"
                    },
                    {
                        "Description": "Metacam 100mls Liquid",
                        "Type": "Medications-Oral",
                        "AmountExVAT": "85.6818",
                        "InvoiceNumber": "768677",
                        "Date": "2016-12-03"
                    }
                ]

        # Process Excel data in batches of 500 items per API call
        total_rows = len(excel_data) if excel_data else 0
        print(f"📊 Processing {total_rows} products from Excel file")

        batch_size = 500
        total_batches = (total_rows + batch_size - 1) // batch_size if total_rows > 0 else 1
        print(f"📦 Will process in {total_batches} batch(es) of max {batch_size} items each")

        all_output_data = []  # Collect all output data from all batches

        # Process data in batches
        for batch_num in range(total_batches):
            start_idx = batch_num * batch_size
            end_idx = min(start_idx + batch_size, total_rows)

            if total_rows > 0:
                batch_data = excel_data[start_idx:end_idx]
                print(f"\n🔄 Processing batch {batch_num + 1}/{total_batches} (items {start_idx + 1}-{end_idx})")
            else:
                batch_data = []
                print(f"\n🔄 Processing batch {batch_num + 1}/{total_batches} (using default data)")

            # Generate invoice items for this batch
            invoice_items = generate_invoice_items(batch_data, max_items=None)
            print(f"📋 Generated {len(invoice_items)} invoice items for this batch")

            # Show first few invoice items for verification
            if invoice_items and batch_num == 0:  # Only show for first batch
                print("📝 First few invoice items:")
                for i, item in enumerate(invoice_items[:3]):
                    print(f"  {i+1}. {item['Description']} - {item['Type']} - ${item['AmountExVAT']}")
                if len(invoice_items) > 3:
                    print(f"  ... and {len(invoice_items) - 3} more items")

            # --- API Calls for this batch ---
            print(f"🚀 Starting API process for batch {batch_num + 1}...")

            # --- 1. Get ConversationID ---
            print(f"--- Step 1: Getting Conversation ID for batch {batch_num + 1} ---")
            new_conversation_url = f"{base_url}/api/Claim/NewConversationId"

            # Prepare authentication headers
            headers = {
                "TransactionId": f"EzyVetClaim{env_vars.get('date', '')}3",
                "VendorPassword": env_vars.get('vendorpassword', ''),
                "UserID": env_vars.get('userIDEzyVet', ''),
                "UserPassword": env_vars.get('userPasswordEzyVet', ''),
                "FormatType": "JSON"
            }

            try:
                response_conv = requests.get(new_conversation_url, headers=headers)
                response_conv.raise_for_status()

                try:
                    response_data = response_conv.json()

                    # Handle the actual response structure: {'c': {'id': 'conversation-id'}}
                    conversation_id = None
                    if 'c' in response_data and 'id' in response_data['c']:
                        conversation_id = response_data['c']['id']
                    elif 'ConversationId' in response_data:
                        conversation_id = response_data['ConversationId']

                    if conversation_id:
                        print(f"✅ Successfully retrieved ConversationID: {conversation_id}")

                        # Create output data for this batch
                        current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        batch_output_data = []
                        for item in invoice_items:
                            batch_output_data.append({
                                'Product Name': item['Description'],
                                'Type': item['Type'],
                                'Amount': item['AmountExVAT'],
                                'ConversationID': conversation_id,
                                'Timestamp': current_timestamp,
                                'Batch': batch_num + 1
                            })

                        # Add to overall output data
                        all_output_data.extend(batch_output_data)

                        # Wait 2 seconds before next API call
                        print("⏳ Waiting 2 seconds before next API call...")
                        time.sleep(2)

                    else:
                        print("❌ Could not find conversation ID in the response.")
                        print(f"Available keys in response: {list(response_data.keys())}")
                        continue  # Skip this batch
                except json.JSONDecodeError as e:
                    print(f"❌ Failed to parse JSON response: {e}")
                    continue  # Skip this batch
            except requests.exceptions.RequestException as e:
                print(f"Error during Get ConversationID request: {e}")
                continue  # Skip this batch

            # --- 2. Get AttachmentID ---
            print(f"--- Step 2: Getting Attachment ID for batch {batch_num + 1} ---")
            try:
                new_attachment_url = f"{base_url}/api/Claim/{conversation_id}/Attachment/NewConversationId"

                response_attach_id = requests.get(new_attachment_url, headers=headers)
                response_attach_id.raise_for_status()

                try:
                    response_data = response_attach_id.json()

                    # Handle different possible response structures
                    attachment_id = None
                    if 'c' in response_data and 'id' in response_data['c']:
                        attachment_id = response_data['c']['id']
                    elif 'a' in response_data and 'id' in response_data['a']:
                        attachment_id = response_data['a']['id']
                    elif 'AttachmentId' in response_data:
                        attachment_id = response_data['AttachmentId']
                    elif 'id' in response_data:
                        attachment_id = response_data['id']

                    if attachment_id:
                        print(f"✅ Successfully retrieved AttachmentID: {attachment_id}")

                        # Wait 2 seconds before next API call
                        print("⏳ Waiting 2 seconds before next API call...")
                        time.sleep(2)
                    else:
                        print("❌ Could not find attachment ID in the response.")
                        print(f"Available keys in response: {list(response_data.keys())}")
                        continue  # Skip this batch
                except json.JSONDecodeError as e:
                    print(f"❌ Failed to parse JSON response: {e}")
                    continue  # Skip this batch
            except requests.exceptions.RequestException as e:
                print(f"Error during Get AttachmentID request: {e}")
                continue  # Skip this batch


            # --- 3. Submit Attachment (Clinical) ---
            print(f"--- Step 3: Submitting Attachment for batch {batch_num + 1} ---")
            # You will need to specify the path to your clinical attachment file
            attachment_file_path = '/home/<USER>/repos/sophia_junior/test_print.pdf'

            if not os.path.exists(attachment_file_path):
                print(f"❌ Attachment file not found at: {attachment_file_path}")
                continue  # Skip this batch

            submit_attachment_url = f"{base_url}/api/Claim/{conversation_id}/Attachment/{attachment_id}"

            try:
                # Prepare headers for attachment submission
                attachment_headers = {
                    "FormatType": "JSON",
                    "DocumentName": os.path.basename(attachment_file_path),
                    "TransactionId": f"EzyVetClaim{env_vars.get('date', '')}a",
                    "VendorPassword": env_vars.get('vendorpassword', ''),
                    "UserID": env_vars.get('userIDEzyVet', ''),
                    "UserPassword": env_vars.get('userPasswordEzyVet', ''),
                    "Content-Type": "application/pdf"
                }

                with open(attachment_file_path, 'rb') as f:
                    files = {'file': (os.path.basename(attachment_file_path), f, 'application/pdf')}
                    response_submit_attach = requests.post(submit_attachment_url, files=files, headers=attachment_headers)
                    response_submit_attach.raise_for_status()
                print(f"✅ Attachment submitted successfully. Status Code: {response_submit_attach.status_code}")

                # Wait 2 seconds before next API call
                print("⏳ Waiting 2 seconds before next API call...")
                time.sleep(2)
            except requests.exceptions.RequestException as e:
                print(f"Error during Submit Attachment request: {e}")
                continue  # Skip this batch
            except FileNotFoundError:
                print(f"❌ Attachment file not found at path: {attachment_file_path}")
                continue  # Skip this batch

            # --- 4. Submit Claim ---
            print(f"--- Step 4: Submitting Claim for batch {batch_num + 1} ---")
            submit_claim_url = f"{base_url}/api/Claim/{conversation_id}"

            # Prepare headers for claim submission
            claim_headers = {
                "TransactionId": f"EzyVetClaim{env_vars.get('date', '')}3",
                "VendorPassword": env_vars.get('vendorpassword', ''),
                "UserID": env_vars.get('userIDEzyVet', ''),
                "UserPassword": env_vars.get('userPasswordEzyVet', ''),
                "FormatType": "JSON",
                "Content-Type": "application/json"
            }

            # You need to provide the JSON body for the claim submission
            # This is an example, modify it to match your actual claim data
            claim_data = {
                "Identification": {
                    "OwnerID": "45652",
                    "PracticeID": "petsure",
                    "PracticeClaimRef": "1545",
                    "RequestGap": "Y"
                },
                "InfoFromPolicyHolder": {
                "PolicyDetails": {
                "PolicyNumber": "WP00003731S",
                "PolicyholderName": "Stella Seater",
                "Address": "5 Laurie Court, SKYE, VIC",
                "Postcode": "3977",
                "EmailAddress": "<EMAIL>",
                "PreferredContactBy": "Email",
                "InsuranceCompany": "JSON Compnay",
                "ExpiryDate": "2020-09-23"
                },
                "AnimalDetails": {
                "Name": "Holly",
                "Species": "Canine",
                "Breed": "Rottweiler",
                "DateOfBirth": "2014-09-23",
                "Gender": "Female",
                "Colour": "Black & Tan",
                "MicrochipNumber": ""
                },
                "Conditions": [],
                "Financial": {
                "PayVet": "true",
                "PayClaimTo": "vet"
                }
            },
            "InfoFromVet": {
                "Miscellaneous": {
                
                },
                "Vet": {
                "VetSurname": "Morgan Woodforde BVMS BSc ",
                "VetForenames": "Dr",
                "PracticeName": "REMTest",
                "PracticeAddress": "REMTest",
                "PracticePhone": "9277 7488",
                "PracticePostcode": "2069",
                "RegistrationNumber": "420",
                },
                "AnimalClinicalHistory": [

                ],
                "Conditions": [
                {
                    "DiagnosisOrSigns": "Torn Nail (different nail from last time)123",
                    "ClaimContinuation": "0",
                "ConditionCode": "Code1223",
                    "Started": "2016-12-03",
                    "DeathOrEuthanasia": "false",
                    "OngoingCondition": "false",
                    "TreatmentDates": {
                    "DateFrom": "2016-12-03",
                    "DateTo": "2016-12-03"
                    },
                    "Financial": {
                    "TotalExVAT": "214.7727",
                    "VAT": "11.4773",
                    "TotalIncVat": "126.2700",
                    "TotalDiscount": "57.59",
                    "InvoiceItems": invoice_items,
                    "Invoices": [
                        {
                        "Invoice": {
                            "InvoiceNumber": "test_invoicenumber",
                            "Date": "2015-12-03",
                            "TotalExVAT": "30.02",
                            "TotalDiscountExVAT": "125.58",
                            "VAT": "12.23",
                            "TotalIncVat": "158",
                            "Items": [
                            {
                                "Item": {
                                "ItemCode": "test_itemcode",
                                "Sequence": "test_seq1",
                                "Description": "test_desc",
                                "Type": "test_type",
                                "AmountExVAT": "5.59",
                                "DiscountExVAT": "58.65",
                                "VAT": "98.98",
                                "Quantity": "45.59",
                                "TotalIncVAT": "98.59"
                                }
                            }
                            ]
                        }
                        }
                    ]
                    },
                        }
                ],
                "Referrals": [
                ]
            }
            }

            try:
                # Debug: Show claim data structure before sending
                print(f"🔍 Sending claim with {len(claim_data['InfoFromVet']['Conditions'][0]['Financial']['InvoiceItems'])} invoice items")

                response_submit_claim = requests.post(submit_claim_url, json=claim_data, headers=claim_headers)
                response_submit_claim.raise_for_status()
                print(f"✅ Claim submitted successfully! Status Code: {response_submit_claim.status_code}")
                print(f"✅ Batch {batch_num + 1}/{total_batches} completed successfully!")

                # Wait 3 seconds after completing a batch
                if batch_num + 1 < total_batches:  # Don't wait after the last batch
                    print("⏳ Waiting 3 seconds before starting next batch...")
                    time.sleep(3)

            except requests.exceptions.RequestException as e:
                print(f"Error during Submit Claim request for batch {batch_num + 1}: {e}")
                print(f"Response content: {e.response.text if hasattr(e, 'response') and e.response else 'No response content'}")
                continue  # Skip this batch but continue with next batch

        # End of batch processing loop
        print(f"\n🎉 All {total_batches} batches completed!")
        print(f"📊 Total products processed: {len(all_output_data)}")

        # Save all output data to spreadsheet (append to existing file)
        try:
            output_filename = f'/home/<USER>/repos/sophia_junior/vpp_product_BFP 2.0_output2.xlsx'
            new_df = pd.DataFrame(all_output_data)

            # Check if file exists and append to it
            if os.path.exists(output_filename):
                # Read existing data
                existing_df = pd.read_excel(output_filename)
                # Append new data
                combined_df = pd.concat([existing_df, new_df], ignore_index=True)
                print(f"📊 Appending {len(all_output_data)} new records to existing file")
            else:
                combined_df = new_df
                print(f"📊 Creating new output file with {len(all_output_data)} records")

            # Save combined data
            combined_df.to_excel(output_filename, index=False)
            print(f"📊 Output saved to: {output_filename}")
            print(f"📋 Total records in file: {len(combined_df)}")

            # Show summary of all ConversationIDs in the file
            unique_conversations = combined_df['ConversationID'].unique()
            print(f"📈 Summary: {len(unique_conversations)} unique ConversationIDs in output file")
            print(f"📈 ConversationIDs: {list(unique_conversations)}")
        except Exception as e:
            print(f"⚠️ Warning: Could not save output spreadsheet: {e}")

        print("\n🎉 All steps completed successfully!")


    except FileNotFoundError as e:
        print(f"❌ Error: {e}. Please make sure the file paths are correct.")
    except json.JSONDecodeError:
        print("❌ Error decoding JSON. Please check the format of your collection and environment files.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    # --- Configuration ---
    # Update these paths if your files are in a different directory
    environment_filename = '/home/<USER>/repos/sophia_junior/scr/environment.json'
    excel_filename = '/home/<USER>/repos/sophia_junior/vpp_product_BFP 2.0.xlsx'  # vpp_product_BFP 2.0.Update this path to your Excel file

   
    # Check if Excel file exists, if not, run without it
    if os.path.exists(excel_filename):
        print(f"📊 Using Excel file: {excel_filename}")
        run_postman_requests(environment_filename, excel_filename)
    else:
        print(f"⚠️ Excel file not found at {excel_filename}, running with default data")
        run_postman_requests(environment_filename)