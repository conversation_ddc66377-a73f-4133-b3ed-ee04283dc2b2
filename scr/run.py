import json
import requests
import os
import pandas as pd
from datetime import datetime

def run_postman_requests(environment_path, excel_file_path=None):
    """
    Runs a series of VetHub API requests for claim submission,
    using environment variables from a Postman environment file.

    Args:
        environment_path (str): The file path for the Postman environment JSON.
        excel_file_path (str, optional): The file path for the Excel file containing product names.
    """
    try:
        with open(environment_path, 'r') as f:
            env_data = json.load(f)
        
        env_vars = {item['key']: item['value'] for item in env_data.get('values', [])}
        base_url = env_vars.get('vethubURL')

        # Debug info (comment out for production)
        # print(f"Debug - Environment variables loaded: {len(env_vars)} variables")
        # print(f"Debug - Base URL: {base_url}")
        # print(f"Debug - UserID: {env_vars.get('userIDEzyVet', 'NOT_FOUND')}")
        # print(f"Debug - Date: {env_vars.get('date', 'NOT_FOUND')}")

        if not base_url:
            print("❌ 'vethubURL' not found in the environment file.")
            return

        # Read product data from Excel file if provided
        excel_data = []
        if excel_file_path:
            try:
                print(f"📊 Reading Excel file: {excel_file_path}")
                df = pd.read_excel(excel_file_path)
                if 'Product Name' in df.columns:
                    # Convert DataFrame to list of dictionaries for easier processing
                    excel_data = df.to_dict('records')
                    print(f"✅ Found {len(excel_data)} products in Excel file")
                    print(f"Available columns: {list(df.columns)}")
                    print(f"First few products: {[row['Product Name'] for row in excel_data[:3]]}")
                else:
                    print("❌ 'Product Name' column not found in Excel file")
                    print(f"Available columns: {list(df.columns)}")
                    return
            except Exception as e:
                print(f"❌ Error reading Excel file: {e}")
                return
        else:
            print("ℹ️ No Excel file provided, using default product descriptions")

        # Note: We're not actually using the collection file in this implementation
        # The collection file was used as reference to understand the API structure
        # with open(collection_path, 'r') as f:
        #     collection_data = json.load(f)

        # --- 1. Get ConversationID ---
        print("🚀 Starting the process...")
        print("\n--- Step 1: Getting Conversation ID ---")
        new_conversation_url = f"{base_url}/api/Claim/NewConversationId"

        # Prepare authentication headers
        headers = {
            "TransactionId": f"EzyVetClaim{env_vars.get('date', '')}3",
            "VendorPassword": env_vars.get('vendorpassword', ''),
            "UserID": env_vars.get('userIDEzyVet', ''),
            "UserPassword": env_vars.get('userPasswordEzyVet', ''),
            "FormatType": "JSON"
        }

        # Debug info (comment out for production)
        # print(f"Making request to: {new_conversation_url}")
        # print(f"Headers: {headers}")

        try:
            response_conv = requests.get(new_conversation_url, headers=headers)

            # Debug: Print the response to see the actual structure (comment out for production)
            # print(f"Response Status Code: {response_conv.status_code}")
            # print(f"Response Content: {response_conv.text}")

            response_conv.raise_for_status()  # Raises an HTTPError for bad responses (4xx or 5xx)

            try:
                response_data = response_conv.json()
                # print(f"Parsed JSON: {response_data}")  # Debug output

                # Handle the actual response structure: {'c': {'id': 'conversation-id'}}
                conversation_id = None
                if 'c' in response_data and 'id' in response_data['c']:
                    conversation_id = response_data['c']['id']
                elif 'ConversationId' in response_data:
                    conversation_id = response_data['ConversationId']

                if conversation_id:
                    print(f"✅ Successfully retrieved ConversationID: {conversation_id}")

                    # Update output data with ConversationID
                    for item in output_data:
                        item['ConversationID'] = conversation_id

                else:
                    print("❌ Could not find conversation ID in the response.")
                    print(f"Available keys in response: {list(response_data.keys())}")
                    return
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                return
        except requests.exceptions.RequestException as e:
            print(f"Error during Get ConversationID request: {e}")
            return

        # --- 2. Get AttachmentID ---
        print("\n--- Step 2: Getting Attachment ID ---")
        try:
            new_attachment_url = f"{base_url}/api/Claim/{conversation_id}/Attachment/NewConversationId"

            # Use the same authentication headers
            headers = {
                "TransactionId": f"EzyVetClaim{env_vars.get('date', '')}3",
                "VendorPassword": env_vars.get('vendorpassword', ''),
                "UserID": env_vars.get('userIDEzyVet', ''),
                "UserPassword": env_vars.get('userPasswordEzyVet', ''),
                "FormatType": "JSON"
            }

            response_attach_id = requests.get(new_attachment_url, headers=headers)

            # Debug: Print the response to see the actual structure (comment out for production)
            # print(f"Attachment Response Status Code: {response_attach_id.status_code}")
            # print(f"Attachment Response Content: {response_attach_id.text}")

            response_attach_id.raise_for_status()

            try:
                response_data = response_attach_id.json()
                # print(f"Attachment Parsed JSON: {response_data}")  # Debug output

                # Handle different possible response structures
                attachment_id = None
                if 'c' in response_data and 'id' in response_data['c']:
                    attachment_id = response_data['c']['id']
                elif 'a' in response_data and 'id' in response_data['a']:
                    attachment_id = response_data['a']['id']
                elif 'AttachmentId' in response_data:
                    attachment_id = response_data['AttachmentId']
                elif 'id' in response_data:
                    attachment_id = response_data['id']

                if attachment_id:
                    print(f"✅ Successfully retrieved AttachmentID: {attachment_id}")
                else:
                    print("❌ Could not find attachment ID in the response.")
                    print(f"Available keys in response: {list(response_data.keys())}")
                    return
            except json.JSONDecodeError as e:
                print(f"❌ Failed to parse JSON response: {e}")
                return
        except requests.exceptions.RequestException as e:
            print(f"Error during Get AttachmentID request: {e}")
            return


        # --- 3. Submit Attachment (Clinical) ---
        print("\n--- Step 3: Submitting Attachment ---")
        # You will need to specify the path to your clinical attachment file
        attachment_file_path = '/home/<USER>/repos/sophia_junior/test_print.pdf' # <--- CHANGE THIS

        if not os.path.exists(attachment_file_path):
            print(f"❌ Attachment file not found at: {attachment_file_path}")
            return

        submit_attachment_url = f"{base_url}/api/Claim/{conversation_id}/Attachment/{attachment_id}"

        try:
            # Prepare headers for attachment submission (don't include Content-Type for file uploads)
            headers = {
                "FormatType": "JSON",
                "DocumentName": os.path.basename(attachment_file_path),
                "TransactionId": f"EzyVetClaim{env_vars.get('date', '')}a",
                "VendorPassword": env_vars.get('vendorpassword', ''),
                "UserID": env_vars.get('userIDEzyVet', ''),
                "UserPassword": env_vars.get('userPasswordEzyVet', ''),
                "Content-Type": "application/pdf"
            }

            with open(attachment_file_path, 'rb') as f:
                files = {'file': (os.path.basename(attachment_file_path), f, 'application/pdf')}
                response_submit_attach = requests.post(submit_attachment_url, files=files, headers=headers)

                # Debug: Print response details for troubleshooting (comment out for production)
                # print(f"Attachment Submit Response Status Code: {response_submit_attach.status_code}")
                # print(f"Attachment Submit Response Content: {response_submit_attach.text}")

                response_submit_attach.raise_for_status()
            print(f"✅ Attachment submitted successfully. Status Code: {response_submit_attach.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"Error during Submit Attachment request: {e}")
            return
        except FileNotFoundError:
            print(f"❌ Attachment file not found at path: {attachment_file_path}")
            return


        # --- 4. Submit Claim ---
        print("\n--- Step 4: Submitting Claim ---")
        submit_claim_url = f"{base_url}/api/Claim/{conversation_id}"

        # Prepare headers for claim submission
        claim_headers = {
            "TransactionId": f"EzyVetClaim{env_vars.get('date', '')}3",
            "VendorPassword": env_vars.get('vendorpassword', ''),
            "UserID": env_vars.get('userIDEzyVet', ''),
            "UserPassword": env_vars.get('userPasswordEzyVet', ''),
            "FormatType": "JSON",
            "Content-Type": "application/json"
        }

        # Generate invoice items based on Excel data or use defaults (limit to 100 items per call)
        def generate_invoice_items(excel_data, max_items=100):
            if excel_data:
                # Limit to max_items (100) per call
                limited_data = excel_data[:max_items]
                # Create invoice items from Excel data
                invoice_items = []
                for i, row in enumerate(limited_data):
                    # Get values from Excel or use defaults
                    description = row.get('Product Name', f'Product {i+1}')
                    item_type = ''
                    amount = row.get('Price', 29.09 + i * 10)

                    item = {
                        "Description": description,
                        "Type": item_type,
                        "AmountExVAT": f"{float(amount):.4f}",
                        "InvoiceNumber": "768677",
                        "Date": "2016-12-03"
                    }
                    invoice_items.append(item)
                return invoice_items
            else:
                # Default invoice items
                return [
                    {
                        "Description": "Sick Patient Revisit Short",
                        "Type": "Examinations",
                        "AmountExVAT": "29.0909",
                        "InvoiceNumber": "768677",
                        "Date": "2016-12-03"
                    },
                    {
                        "Description": "Metacam 100mls Liquid",
                        "Type": "Medications-Oral",
                        "AmountExVAT": "85.6818",
                        "InvoiceNumber": "768677",
                        "Date": "2016-12-03"
                    }
                ]

        invoice_items = generate_invoice_items(excel_data, max_items=100)
        print(f"📋 Generated {len(invoice_items)} invoice items")

        # Show first few invoice items for verification
        if invoice_items:
            print("📝 First few invoice items:")
            for i, item in enumerate(invoice_items[:3]):
                print(f"  {i+1}. {item['Description']} - {item['Type']} - ${item['AmountExVAT']}")
            if len(invoice_items) > 3:
                print(f"  ... and {len(invoice_items) - 3} more items")

        # Prepare data for output spreadsheet
        output_data = []
        for item in invoice_items:
            output_data.append({
                'Product Name': item['Description'],
                'Type': item['Type'],
                'Amount': item['AmountExVAT'],
                'ConversationID': None  # Will be filled after getting the conversation ID
            })

        # You need to provide the JSON body for the claim submission
        # This is an example, modify it to match your actual claim data
        claim_data = {
            "Identification": {
                "OwnerID": "45652",
                "PracticeID": "petsure",
                "PracticeClaimRef": "1545",
                "RequestGap": "Y"
            },
            "InfoFromPolicyHolder": {
                "PolicyDetails": {
                "PolicyNumber": "WP00003731S",
                "PolicyholderName": "Stella Seater",
                "Address": "5 Laurie Court, SKYE, VIC",
                "Postcode": "3977",
                "EmailAddress": "<EMAIL>",
                "PreferredContactBy": "Email",
                "InsuranceCompany": "JSON Compnay",
                "ExpiryDate": "2020-09-23"
                },
                "AnimalDetails": {
                "Name": "Holly",
                "Species": "Canine",
                "Breed": "Rottweiler",
                "DateOfBirth": "2014-09-23",
                "Gender": "Female",
                "Colour": "Black & Tan",
                "MicrochipNumber": ""
                },
                "Conditions": [],
                "Financial": {
                "PayVet": "true",
                "PayClaimTo": "vet"
                }
            },
            "InfoFromVet": {
                "Miscellaneous": {
                
                },
                "Vet": {
                "VetSurname": "Morgan Woodforde BVMS BSc ",
                "VetForenames": "Dr",
                "PracticeName": "REMTest",
                "PracticeAddress": "REMTest",
                "PracticePhone": "9277 7488",
                "PracticePostcode": "2069",
                "RegistrationNumber": "420",
                },
                "AnimalClinicalHistory": [

                ],
                "Conditions": [
                {
                    "DiagnosisOrSigns": "Torn Nail (different nail from last time)123",
                    "ClaimContinuation": "0",
                "ConditionCode": "Code1223",
                    "Started": "2016-12-03",
                    "DeathOrEuthanasia": "false",
                    "OngoingCondition": "false",
                    "TreatmentDates": {
                    "DateFrom": "2016-12-03",
                    "DateTo": "2016-12-03"
                    },
                    "Financial": {
                    "TotalExVAT": "214.7727",
                    "VAT": "11.4773",
                    "TotalIncVat": "126.2700",
                    "TotalDiscount": "57.59",
                    "InvoiceItems": invoice_items,
                    "Invoices": [
                        {
                        "Invoice": {
                            "InvoiceNumber": "test_invoicenumber",
                            "Date": "2015-12-03",
                            "TotalExVAT": "30.02",
                            "TotalDiscountExVAT": "125.58",
                            "VAT": "12.23",
                            "TotalIncVat": "158",
                            "Items": [
                            {
                                "Item": {
                                "ItemCode": "test_itemcode",
                                "Sequence": "test_seq1",
                                "Description": "test_desc",
                                "Type": "test_type",
                                "AmountExVAT": "5.59",
                                "DiscountExVAT": "58.65",
                                "VAT": "98.98",
                                "Quantity": "45.59",
                                "TotalIncVAT": "98.59"
                                }
                            }
                            ]
                        }
                        }
                    ]
                    },
                        }
                ],
                "Referrals": [
                ]
            }
}

        try:
            response_submit_claim = requests.post(submit_claim_url, json=claim_data, headers=claim_headers)
            response_submit_claim.raise_for_status()
            print(f"✅ Claim submitted successfully! Status Code: {response_submit_claim.status_code}")
            print("Response Body:")
            print(f"{response_submit_claim.text}")
            # Try to parse as JSON, but don't fail if it's not JSON
            try:
                json_response = response_submit_claim.json()
                print(f"JSON Response: {json_response}")
            except json.JSONDecodeError:
                pass  # Response is plain text, which is fine
        except requests.exceptions.RequestException as e:
            print(f"Error during Submit Claim request: {e}")
            return

        # Save output data to spreadsheet
        try:
            output_df = pd.DataFrame(output_data)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f'/home/<USER>/repos/sophia_junior/claim_output.xlsx'
            output_df.to_excel(output_filename, index=False)
            print(f"📊 Output saved to: {output_filename}")
            print(f"📋 Saved {len(output_data)} products with ConversationID: {conversation_id}")
        except Exception as e:
            print(f"⚠️ Warning: Could not save output spreadsheet: {e}")

        print("\n🎉 All steps completed successfully!")


    except FileNotFoundError as e:
        print(f"❌ Error: {e}. Please make sure the file paths are correct.")
    except json.JSONDecodeError:
        print("❌ Error decoding JSON. Please check the format of your collection and environment files.")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")

if __name__ == '__main__':
    # --- Configuration ---
    # Update these paths if your files are in a different directory
    environment_filename = '/home/<USER>/repos/sophia_junior/scr/environment.json'
    excel_filename = '/home/<USER>/repos/sophia_junior/test.xlsx'  # vpp_product_BFP 2.0 Update this path to your Excel file

    # Excel file requirements:
    # - Must have a 'Product Name' column (required)
    # - Optional columns: 'Category' (for Type), 'Price' (for AmountExVAT)
    # - If optional columns are missing, default values will be used

    # Check if Excel file exists, if not, run without it
    if os.path.exists(excel_filename):
        print(f"📊 Using Excel file: {excel_filename}")
        run_postman_requests(environment_filename, excel_filename)
    else:
        print(f"⚠️ Excel file not found at {excel_filename}, running with default data")
        run_postman_requests(environment_filename)