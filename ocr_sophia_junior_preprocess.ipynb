import json, os, pickle
import pandas as pd
import numpy as np
# import matplotlib.pyplot as plt
# import seaborn as sns

import pyodbc
import requests
import urllib

from tqdm import tqdm
from typing import Optional

from pathlib import Path

ROOTDIR = Path("/home/<USER>/repos/sophia_junior")
vp = pd.read_excel(ROOTDIR / "vpp_product_BFP 2.0.xlsx")

vp_test = vp.head(20)


vp_test.to_excel(ROOTDIR / "test.xlsx", index=False)

vp['ID']= range(1, len(vp)+1)
vp = vp.rename(columns={"Product Name": 'invoice_description'})
vp['invoice_line_amount_actual_aud'] = None

len(vp)

def get_prediction(data):
    # print('dd', data)
    endpoint = "https://ps-uat-ca-tstar.australiaeast.inference.ml.azure.com/score"
    token = '1dsZRUaQLXrIn0pdl8R1jyF78SoAyqKhxSECArthEjLRRQbC5anaJQQJ99BEAAAAAAAAAAAAINFRAZML4Xpk'
    bearer_token = 'Bearer ' + token
    headers = {"Authorization": bearer_token, "Content-Type": 'application/json'}
    response = requests.post(endpoint, data=data, headers=headers)


    try:
        prediction = response.json()
    except:
        prediction = response
    # print('pp', prediction)
    return prediction


def get_business_rules(
    url: str = "https://ps-uat-fapp-cabusinessrules.azurewebsites.net/api/RunLRules",
    api_key: str = "2ElLOXvoKYIatnZjtREnZTsKb4sX0AFIMHHlY76CcnjXpL3CiRRpwg==",
    body: str = "",
) -> Optional[bytes]:
    headers = {"Content-Type": "application/json", "Authorization": ("code " + api_key)}
    req = urllib.request.Request(url, str.encode(body), headers)

    try:
        response = urllib.request.urlopen(req)
        result = response.read()
        return result
    except urllib.error.HTTPError as error:
        print("The request failed with status code " + str(error.code))
        return None


import requests
from typing import Optional

def get_business_rules_postman(
    url: str,
    api_key: str,
    body: dict
) -> Optional[dict]:
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"code {api_key}"
    }

    try:
        response = requests.post(url, json=body, headers=headers)
        response.raise_for_status()  # Raise an exception for HTTP errors
        return response.json()  # or response.text if you expect plain text
    except requests.exceptions.HTTPError as err:
        print(f"HTTP error occurred: {err} - Status Code: {response.status_code}")
    except requests.exceptions.RequestException as err:
        print(f"Request failed: {err}")
    return None

url ="https://ps-uat-fapp-cabusinessrules.azurewebsites.net/api/RunLRules"
api_key = "2ElLOXvoKYIatnZjtREnZTsKb4sX0AFIMHHlY76CcnjXpL3CiRRpwg=="
body = {
  "conversationId": "355ceebd-ec61-4061-a849-7af238ced286",
  "CorrelationId": "071968f3-5c16-4523-9f57-45487cbfb52c",
  "processedDateTime": "2021-06-16T12:14:15.9845860",
  "product": "ClaimsAutomation",
  "PolicyNumber": "WP-0000-0022-24",
  "version": {
    "releaseVersion": "2",
    "dStarCAPIVersion": "5",
    "tStarCAPIVersion": "6",
    "tSTarClusterVersion": "4",
    "nStarSeenVersion": "3",
    "breedVersion": "1"
  },
  "DStarPredict": {
    "diagnosis": null,
    "animalDetails": {
      "dateOfBirth": "2020-01-01T00:00:00",
      "sex": null,
      "breed": null,
      "name": "Not required",
      "claimCreateDate": "2021-01-01T00:00:00"
    },
    "version": 1.2
  },
  "NStarPredict": null,
  "TStarPredict": {
    "invoiceItems": [
      {
        "id": 1,
        "t": "Account Prepayment / Overpayment",
        "amountExVat": 1,
        "invoiceDate": "1970-06-16T00:00:00",
        "treatmentDate": "1970-06-16T00:00:00",
        "totalIncVat": 1,
        "invoiceNumber": "********"
      }
    ],
    "treatmentLines": [
      {
        "id": 1,
        "predict": [
          {
            "tStar": "tstarc_miscellaneous",
            "confidence": "0.19"
          },
          {
            "tStar": "tstarc_procedure_fee",
            "confidence": "0.15"
          },
          {
            "tStar": "tstarc_other",
            "confidence": "0.11"
          },
          {
            "tStar": "tstarc_unrelated_treatment",
            "confidence": "0.10"
          },
          {
            "tStar": "tstarc_hospitalisation",
            "confidence": "0.09"
          }
        ]
      }
    ],
    "adjustment": 0,
    "invoiceTotal": 1,
    "invoiceDiscount": null
  }
}


response = get_business_rules_postman(url, api_key, body)

import json
import numpy as np

def read_json(df):
    # Check if required columns exist in the DataFrame
    required_columns = ['ID', 'invoice_description', 'invoice_line_amount_actual_aud']
    for col in required_columns:
        if col not in df.columns:
            raise ValueError(f"Column '{col}' not found in DataFrame")

    # Convert DataFrame to list of dictionaries
    t_arr = df[required_columns].rename(columns={
        'ID': 'Pair_ID',
        'invoice_description': 'T',
        'invoice_line_amount_actual_aud': 'AmountExVat'
    })

    # Convert numpy.int64 to Python int and other types to their native Python equivalents
    t_arr = t_arr.applymap(lambda x: x.item() if isinstance(x, np.generic) else x)

    return json.loads(t_arr.to_json(orient='records'))

# Assuming ezv is your DataFrame
traw_dict = read_json(vp)


len(traw_dict)

traw_dict[0]

for traw_i, traw_v in tqdm(enumerate(traw_dict), total=len(traw_dict)):
    print( traw_v)

traw_dict1 = traw_dict[16479:]
len(traw_dict1)

traw_dict1 = traw_dict[2:5]

## Handle null value

t_id = []
t_raw = []
pred_conf1 = []
pred_conf2 = []
pred_conf3 = []
pred_conf4 = []
pred_conf5 = []
pred_cc1 = []
pred_cc2 = []
pred_cc3 = []
pred_cc4 = []
pred_cc5 = []
lodge_rule = []
tstar_token = []

for traw_i, traw_v in tqdm(enumerate(traw_dict1), total=len(traw_dict1)):
    # print('vv', traw_v)

    if traw_v['T'] is None:
        # Append None for all output lists
        t_id.append(traw_v['Pair_ID'])
        t_raw.append(None)
        pred_conf1.append(None)
        pred_conf2.append(None)
        pred_conf3.append(None)
        pred_conf4.append(None)
        pred_conf5.append(None)
        pred_cc1.append(None)
        pred_cc2.append(None)
        pred_cc3.append(None)
        pred_cc4.append(None)
        pred_cc5.append(None)
        lodge_rule.append(None)
        tstar_token.append(None)
        continue

    try:
        # Check if 'T' is not None and is a valid number or string
        if traw_v['T'] is not None:
            
            float(traw_v['T'])
            traw_v['T'] = str(traw_v['T']) + '-'
     
    except ValueError:
        pass


    data = {
            "conversationId": "355ceebd-ec61-4061-a849-7af238ced286",
            "CorrelationId": "071968f3-5c16-4523-9f57-45487cbfb52c",
            "processedDateTime": "2021-06-16T12:14:15.9845860",
            "product": "ClaimsAutomation",
            "PolicyNumber": "WP-0000-0022-24",
            "version": {
                "releaseVersion": "2",
                "dStarCAPIVersion": "5",
                "tStarCAPIVersion": "6",
                "tSTarClusterVersion": "4",
                "nStarSeenVersion": "3",
                "breedVersion": "1"
                },
            "DStarPredict": {
                "diagnosis": None,
                "animalDetails": {
                    "dateOfBirth": "2020-01-01T00:00:00",
                    "sex": None,
                    "breed": None,
                    "name": "Not required",
                    "claimCreateDate": "2021-01-01T00:00:00"
                },
                "version": 1.2
            },
            "NStarPredict": None,
            "TStarPredict": {
                "invoiceItems": [
                    {
                        "id": 1,
                        "t": "General Anaesthesia - Isoflurane",
                        "amountExVat": 1,
                        "invoiceDate": "1970-06-16T00:00:00",
                        "treatmentDate": "1970-06-16T00:00:00",
                        "totalIncVat": 1,
                        "invoiceNumber": "********"
                    }],
                "treatmentLines": [
                    {
                        "id": 1,
                        "predict": [
                            {
                                "tStar": "tstarc_anaesthesia_-_inhalation_anaesthetic",
                                "confidence": "1.0"
                            },
                            {
                                "tStar": "tstarc_anaesthesia_-_monitoring",
                                "confidence": "0.0"
                            },
                            {
                                "tStar": "tstarc_sedation_for_procedure",
                                "confidence": "0.0"
                            },
                            {
                                "tStar": "tstarc_anaesthesia_-_local_anaesthesia",
                                "confidence": "0.0"
                            },
                            {
                                "tStar": "tstarc_routine_teeth_cleaning",
                                "confidence": "0.0"
                            }
                            ]
                    }],
                "adjustment": 0,
                "invoiceTotal": 1,
                "invoiceDiscount": None
                }
            }

    pred = get_prediction('[' + str(json.dumps(traw_v)) + ']')
    print('[' + str(json.dumps(traw_v)) + ']')
    # print(pred)
        

    data['TStarPredict']['invoiceItems'][0]['t'] = traw_v['T']
    data['TStarPredict']['treatmentLines'][0]['predict'][0]['tStar'] = pred['Predictions'][0]['Predict1']
    data['TStarPredict']['treatmentLines'][0]['predict'][1]['tStar'] = pred['Predictions'][0]['Predict2']
    data['TStarPredict']['treatmentLines'][0]['predict'][2]['tStar'] = pred['Predictions'][0]['Predict3']
    data['TStarPredict']['treatmentLines'][0]['predict'][3]['tStar'] = pred['Predictions'][0]['Predict4']
    data['TStarPredict']['treatmentLines'][0]['predict'][4]['tStar'] = pred['Predictions'][0]['Predict5']
    data['TStarPredict']['treatmentLines'][0]['predict'][0]['confidence'] = pred['Predictions'][0]['Predict1_confidence']
    data['TStarPredict']['treatmentLines'][0]['predict'][1]['confidence'] = pred['Predictions'][0]['Predict2_confidence']
    data['TStarPredict']['treatmentLines'][0]['predict'][2]['confidence'] = pred['Predictions'][0]['Predict3_confidence']
    data['TStarPredict']['treatmentLines'][0]['predict'][3]['confidence'] = pred['Predictions'][0]['Predict4_confidence']
    data['TStarPredict']['treatmentLines'][0]['predict'][4]['confidence'] = pred['Predictions'][0]['Predict5_confidence']
    print('Business Rule starts')
    print(json.dumps(data))
    brule = get_business_rules(body=json.dumps(data))
    print('Business Rule completed')
    brule = json.loads(brule.decode("utf-8"))
    
    t_id.append(traw_v['Pair_ID'])
    t_raw.append(traw_v['T'] )
    pred_conf1.append(pred['Predictions'][0]['Predict1_confidence'])
    pred_conf2.append(pred['Predictions'][0]['Predict2_confidence'])
    pred_conf3.append(pred['Predictions'][0]['Predict3_confidence'])
    pred_conf4.append(pred['Predictions'][0]['Predict4_confidence'])
    pred_conf5.append(pred['Predictions'][0]['Predict5_confidence'])
    pred_cc1.append(pred['Predictions'][0]['Predict1'])
    pred_cc2.append(pred['Predictions'][0]['Predict2'])
    pred_cc3.append(pred['Predictions'][0]['Predict3'])
    pred_cc4.append(pred['Predictions'][0]['Predict4'])
    pred_cc5.append(pred['Predictions'][0]['Predict5'])
    
    lodge_rule.append(brule['treatmentLines'][0]['ruleId'])
    tstar_token.append(brule['treatmentLines'][0]['tStarId']) 


len(tstar_token)

len(tstar_token)

# t_id = []
# t_raw = []
# pred_conf1 = []
# pred_conf2 = []
# pred_conf3 = []
# pred_conf4 = []
# pred_conf5 = []
# pred_cc1 = []
# pred_cc2 = []
# pred_cc3 = []
# pred_cc4 = []
# pred_cc5 = []
# lodge_rule = []
# tstar_token = []

# for traw_i, traw_v in tqdm(enumerate(traw_dict), total=len(traw_dict)):
#     # print('vv', traw_v)
#     try:
#         # Check if 'T' is not None and is a valid number or string
#         if traw_v['T'] is not None:
#             float(traw_v['T'])
#             traw_v['T'] = traw_v['T'] + '-'
     
#     except ValueError:
#         pass


#     data = {
#             "conversationId": "355ceebd-ec61-4061-a849-7af238ced286",
#             "CorrelationId": "071968f3-5c16-4523-9f57-45487cbfb52c",
#             "processedDateTime": "2021-06-16T12:14:15.9845860",
#             "product": "ClaimsAutomation",
#             "PolicyNumber": "WW00003984",
#             "version": {
#                 "releaseVersion": "2",
#                 "dStarCAPIVersion": "5",
#                 "tStarCAPIVersion": "6",
#                 "tSTarClusterVersion": "4",
#                 "nStarSeenVersion": "3",
#                 "breedVersion": "1"
#                 },
#             "DStarPredict": {
#                 "diagnosis": None,
#                 "animalDetails": {
#                     "dateOfBirth": "2020-01-01T00:00:00",
#                     "sex": None,
#                     "breed": None,
#                     "name": "Not required",
#                     "claimCreateDate": "2021-01-01T00:00:00"
#                 },
#                 "version": 1.2
#             },
#             "NStarPredict": None,
#             "TStarPredict": {
#                 "invoiceItems": [
#                     {
#                         "id": 1,
#                         "t": "General Anaesthesia - Isoflurane",
#                         "amountExVat": 1,
#                         "invoiceDate": "1970-06-16T00:00:00",
#                         "treatmentDate": "1970-06-16T00:00:00",
#                         "totalIncVat": 1,
#                         "invoiceNumber": "********"
#                     }],
#                 "treatmentLines": [
#                     {
#                         "id": 1,
#                         "predict": [
#                             {
#                                 "tStar": "tstarc_anaesthesia_-_inhalation_anaesthetic",
#                                 "confidence": "1.0"
#                             },
#                             {
#                                 "tStar": "tstarc_anaesthesia_-_monitoring",
#                                 "confidence": "0.0"
#                             },
#                             {
#                                 "tStar": "tstarc_sedation_for_procedure",
#                                 "confidence": "0.0"
#                             },
#                             {
#                                 "tStar": "tstarc_anaesthesia_-_local_anaesthesia",
#                                 "confidence": "0.0"
#                             },
#                             {
#                                 "tStar": "tstarc_routine_teeth_cleaning",
#                                 "confidence": "0.0"
#                             }
#                             ]
#                     }],
#                 "adjustment": 0,
#                 "invoiceTotal": 1,
#                 "invoiceDiscount": None
#                 }
#             }

#     pred = get_prediction('[' + str(json.dumps(traw_v)) + ']')
        

#     data['TStarPredict']['invoiceItems'][0]['t'] = traw_v['T']
#     data['TStarPredict']['treatmentLines'][0]['predict'][0]['tStar'] = pred['Predictions'][0]['Predict1']
#     data['TStarPredict']['treatmentLines'][0]['predict'][1]['tStar'] = pred['Predictions'][0]['Predict2']
#     data['TStarPredict']['treatmentLines'][0]['predict'][2]['tStar'] = pred['Predictions'][0]['Predict3']
#     data['TStarPredict']['treatmentLines'][0]['predict'][3]['tStar'] = pred['Predictions'][0]['Predict4']
#     data['TStarPredict']['treatmentLines'][0]['predict'][4]['tStar'] = pred['Predictions'][0]['Predict5']
#     data['TStarPredict']['treatmentLines'][0]['predict'][0]['confidence'] = pred['Predictions'][0]['Predict1_confidence']
#     data['TStarPredict']['treatmentLines'][0]['predict'][1]['confidence'] = pred['Predictions'][0]['Predict2_confidence']
#     data['TStarPredict']['treatmentLines'][0]['predict'][2]['confidence'] = pred['Predictions'][0]['Predict3_confidence']
#     data['TStarPredict']['treatmentLines'][0]['predict'][3]['confidence'] = pred['Predictions'][0]['Predict4_confidence']
#     data['TStarPredict']['treatmentLines'][0]['predict'][4]['confidence'] = pred['Predictions'][0]['Predict5_confidence']

#     brule = get_business_rules(body=json.dumps(data))
#     brule = json.loads(brule.decode("utf-8"))
    
#     t_id.append(traw_v['Pair_ID'])
#     t_raw.append(traw_v['T'] )
#     pred_conf1.append(pred['Predictions'][0]['Predict1_confidence'])
#     pred_conf2.append(pred['Predictions'][0]['Predict2_confidence'])
#     pred_conf3.append(pred['Predictions'][0]['Predict3_confidence'])
#     pred_conf4.append(pred['Predictions'][0]['Predict4_confidence'])
#     pred_conf5.append(pred['Predictions'][0]['Predict5_confidence'])
#     pred_cc1.append(pred['Predictions'][0]['Predict1'])
#     pred_cc2.append(pred['Predictions'][0]['Predict2'])
#     pred_cc3.append(pred['Predictions'][0]['Predict3'])
#     pred_cc4.append(pred['Predictions'][0]['Predict4'])
#     pred_cc5.append(pred['Predictions'][0]['Predict5'])
    
#     lodge_rule.append(brule['treatmentLines'][0]['ruleId'])
#     tstar_token.append(brule['treatmentLines'][0]['tStarId']) 


# create a dataframe with data and column names
data_df = {'TreatmentDrugId': t_id,
        'TreatmentDrugRaw': t_raw,
        'PredictionConfidence_1': pred_conf1,
        'PredictionConfidence_2': pred_conf2,
        'PredictionConfidence_3': pred_conf3,
        'PredictionConfidence_4': pred_conf4,
        'PredictionConfidence_5': pred_conf5,
        'PredictionTStarClusterCode_1':pred_cc1,
        'PredictionTStarClusterCode_2':pred_cc2,
        'PredictionTStarClusterCode_3':pred_cc3,
        'PredictionTStarClusterCode_4':pred_cc4,
        'PredictionTStarClusterCode_5':pred_cc5,
        'LodgementRuleName': lodge_rule,
        'TStarClusterToken':tstar_token
       }
data_df = pd.DataFrame(data_df)
data_df.head()

len(data_df)

# data_batch1 = data_df
# data_batch2 = data_df.copy()

data_batch_final = pd.concat([data_batch1,data_batch2], axis=0)
len(data_batch_final)
data_df = data_batch_final
len(data_df)

data_df.to_excel('VP full extract1 20241126_output_batch2.xlsx',index=False)

data_df.to_excel('EzyVet sample 20240617_output.xlsx',index=False)

output = pd.read_csv('C:/Users/<USER>/Documents/OCR/data csv/invoice/tstar_cluster.csv')

import pandas as pd
import pyodbc
import urllib.parse

lookup_df = pd.read_csv(ROOTDIR / "TRuleMapping_vetpartners_prod_20250401.csv")

# lookup_df = pd.read_excel('TRuleMapping_vetpartners_prod_20241126.xlsx')
# data_df = pd.read_excel('VP Final Mappings for Go Live  20241111_ouput.xlsx')

lookup_df = lookup_df[['VetXMLTreatmentName','TStarCName']]
lookup_df = lookup_df.dropna(subset=['VetXMLTreatmentName'],axis=0)

merge_lookup = data_df.merge(lookup_df,left_on='TreatmentDrugRaw',right_on='VetXMLTreatmentName',how='left')

# Perform case-insensitive join by converting columns to lowercase for the merge
merge_lookup = data_df.assign(TreatmentDrugRaw_lower=data_df['TreatmentDrugRaw'].str.lower()).merge(
    lookup_df.assign(VetXMLTreatmentName_lower=lookup_df['VetXMLTreatmentName'].str.lower()),
    left_on='TreatmentDrugRaw_lower',
    right_on='VetXMLTreatmentName_lower',
    how='left'
)

# Drop the temporary columns used for merging
merge_lookup = merge_lookup.drop(columns=['TreatmentDrugRaw_lower', 'VetXMLTreatmentName_lower'])


len(merge_lookup)

merge_lookup[merge_lookup['TreatmentDrugId'].duplicated()]

def update_merge_lookup(merge_lookup):
    # Check where 'VetXMLTreatmentName' is not null
    mask = merge_lookup['VetXMLTreatmentName'].notnull()
    
    # Update 'TStarClusterToken' where the mask is True
    merge_lookup.loc[mask, 'TStarClusterToken'] = merge_lookup.loc[mask, 'TStarCName']
    
    # Update 'PredictionConfidence_1' to 1 where the mask is True
    merge_lookup.loc[mask, 'PredictionConfidence_1'] = 1
    
    # Update other 'PredictionConfidence' columns to 0 where the mask is True
    confidence_cols = ['PredictionConfidence_2', 'PredictionConfidence_3', 'PredictionConfidence_4', 'PredictionConfidence_5']
    merge_lookup.loc[mask, confidence_cols] = 0
    
    return merge_lookup

updated_merge_output = update_merge_lookup(merge_lookup)

updated_merge_output[updated_merge_output['VetXMLTreatmentName'].notnull()]

updated_merge_output.head()

len(updated_merge_output)

updated_merge_output.to_excel('VP NZ raw extract 20250414 output.xlsx',index=False)

import json
import requests

# === Load environment variables from environment.json ===
with open('Pre-Prod.postman_environment.json') as f:
    env_data = json.load(f)

# Convert variables to a dict
env_vars = {item['key']: item['value'] for item in env_data['values']}
vethub_url = env_vars['vethubURL']

# === Step 1: Get ConversationID ===
conversation_url = f"{vethub_url}/api/claim/NewConversationId"
response = requests.get(conversation_url)
response.raise_for_status()
conversation_id = response.json().get('conversationId') or response.text

print(f"[+] ConversationID: {conversation_id}")

# === Step 2: Get AttachmentID ===
attachment_url = f"{vethub_url}/api/claim/{conversation_id}/Attachment/NewConversationId"
response = requests.get(attachment_url)
response.raise_for_status()
attachment_id = response.json().get('attachmentId') or response.text

print(f"[+] AttachmentID: {attachment_id}")

# === Step 3: Submit Clinical Attachment ===
# Replace this with actual payload and headers from collection.json if available
clinical_attachment_url = f"{vethub_url}/api/claim/{conversation_id}/Attachment"
clinical_payload = {
    "attachmentId": attachment_id,
    "data": "BASE64_ENCODED_DATA",  # Replace with actual data
    "type": "clinical"
}
headers = {
    "Content-Type": "application/json"
}

response = requests.post(clinical_attachment_url, json=clinical_payload, headers=headers)
response.raise_for_status()
print("[+] Clinical attachment submitted")

# === Step 4: Submit Claim ===
claim_url = f"{vethub_url}/api/claim/{conversation_id}"
claim_payload = {
    "claim": {
        "conversationId": conversation_id,
        "details": "CLAIM_DETAILS"  # Replace with actual claim data
    }
}

response = requests.post(claim_url, json=claim_payload, headers=headers)
response.raise_for_status()
print("[+] Claim submitted successfully")


SELECT *
FROM  [ClaimsAutomation].[dbo].[TStarCName]

tstarc_code = pd.read_excel('C:/Users/<USER>/Documents/OCR/data csv/invoice/tstarc_code.xlsx')

tstarc_code['TStarClusterToken'] = tstarc_code['TStarClusterToken'].astype(str)

output

tstarc_code

tstarc_output = pd.merge(output, tstarc_code, on='TStarClusterToken', how='left')

tstarc_output

# 523 entries with unknown prediction from 
tstarc_output.loc[tstarc_output['TStarClusterToken'] == '999']

tstarc_output.to_csv('C:/Users/<USER>/Documents/OCR/data csv/invoice/tstarc_output_final.csv')

import pandas as pd
import pyodbc
import sqlalchemy as sa
import urllib.parse

# read the CSV file into a pandas dataframe
df = pd.read_csv('C:/Users/<USER>/Documents/OCR/data csv/invoice/tstarc_output.csv')

params = urllib.parse.quote_plus(
    "DRIVER={ODBC Driver 17 for SQL Server};SERVER=10.3.0.50;DATABASE=FinanceDB;UID=claimsauto;PWD=******V13/1^F}nd")
engine = sa.create_engine("mssql+pyodbc:///?odbc_connect={}".format(params))

# sql_conn = pyodbc.connect("""
#         DRIVER={SQL Server};
#         SERVER={PS-UAT-AZS-DWH1};
#         DATABASE=BIA;
#         Trusted_Connection=yes
#         """)

# insert the data into a table in the database
# df.to_sql('FinanceDB.dbo.tstarc_ouput', engine, if_exists='replace', index=False)

df.to_sql(
    name="tstarc_output",
    con=engine,
    schema="model",
    chunksize=100000,
    if_exists='replace',
    index=False)

# close the database connection
sql_conn.close()


# and use the variable engine as the second argument in the to_sql method

tstarc_output

claim_df

tstarc_output['ClaimNo'] = claim_df['claimNumber']
tstarc_output['AmountInclVat'] = claim_df['AmountExVat']

tstarc_output

tstarc_bia = pd.read_csv('C:/Users/<USER>/Documents/OCR/data csv/invoice/tstarc_bia.csv')

tstarc_bia['AmountInclVat'] = tstarc_bia['AmountInclVat'].astype(float)
output['AmountInclVat'] = output['AmountInclVat'].astype(float)

merged = pd.merge(output, tstarc_bia, on=['ClaimNo','AmountInclVat'])

merged

merged_left = pd.merge(output, tstarc_bia, on=['ClaimNo','AmountInclVat'], how='left', indicator=True)

# Incorrect amount extraction from upm, so can't match with bia table
merged_left[merged_left['_merge'] == 'left_only']

# identified claim with incorrect ocr amount
tstarc_bia[tstarc_bia['ClaimNo']=='C5362751']

import re

regex = r'_+'
replacement = ' '

merged['TStarClusterToken'] = merged['TStarClusterToken'].apply(lambda x: re.sub(regex, replacement, x[7:]))
merged['TStarClusterToken'] = merged['TStarClusterToken'].apply(lambda x: re.sub(r'\s*-\s*', '-',x))

merged['TStarClusterDescription'] = merged['TStarClusterDescription'].astype(str)
merged['TStarClusterDescription'] = merged['TStarClusterDescription'].apply(lambda x: x.lower())

merged['TStarClusterDescription'] = merged['TStarClusterDescription'].apply(lambda x: re.sub(r'\s*-\s*', '-',x))

merged['if_tstarc_same'] = (merged['TStarClusterToken'] == merged['TStarClusterDescription']).astype(int)

merged['if_tstarc_same'].value_counts()

# Claims with incorrect tstarc prediction
inc_pred = merged[merged['if_tstarc_same']==0][['TStarClusterToken','TStarClusterDescription']]

acc_tstarc = pd.read_excel('C:/Users/<USER>/Documents/OCR/data csv/invoice/acceptable_tstarc.xlsx')

acc_tstarc['acceptable_pred'] = acc_tstarc['acceptable_pred'].apply(lambda x: re.sub(regex, replacement, x[7:]))
acc_tstarc['acceptable_pred'] = acc_tstarc['acceptable_pred'].apply(lambda x: re.sub(r'\s*-\s*', '-',x))

acc_tstarc['true_tstarc'] = acc_tstarc['true_tstarc'].apply(lambda x: re.sub(regex, replacement, x[7:]))
acc_tstarc['true_tstarc'] = acc_tstarc['true_tstarc'].apply(lambda x: re.sub(r'\s*-\s*', '-',x))

acc_tstarc['true_tstarc'].value_counts()

new_acc_tstarc =acc_tstarc[['acceptable_pred','true_tstarc']].groupby(['true_tstarc'])['acceptable_pred'].apply(','.join).reset_index()

new_acc_tstarc['acceptable_pred'] = new_acc_tstarc['acceptable_pred'].apply(lambda x: x.split(','))

tstar_dic = new_acc_tstarc.set_index('true_tstarc')['acceptable_pred'].to_dict()
tstar_dic['procedure fee'][1]

new_acc_tstarc

new_acc_tstarc['acceptable_pred'] = new_acc_tstarc['acceptable_pred'].apply(lambda x: list(x))

merged['final_match'] = merged['if_tstarc_same']

for i in range(len(merged)):
    if merged['final_match'].iloc[i] == 0:
        actual_tstarc = merged['TStarClusterDescription'].iloc[i]
        pred_tstarc =  merged['TStarClusterToken'].iloc[i]

#         print(actual_tstarc)
#         print(pred_tstarc)
        if actual_tstarc in tstar_dic.keys():
            tstar_pred_ls = tstar_dic[actual_tstarc]
#             print(tstar_pred_ls)
            if pred_tstarc in tstar_pred_ls :
                merged['final_match'].iloc[i] = 1
            
        

merged['final_match'].value_counts()

tstar_dic.keys()

merged[(merged['final_match']==0) & (merged['TStarClusterDescription']=='nan')]

merged[(merged['TStarClusterToken']=='other')][['TStarClusterToken','TStarClusterDescription']]

merged[(merged['TStarClusterToken']=='other') & (merged['TStarClusterDescription']=='nan') ]

merged[(merged['TStarClusterToken'].isna()) | (merged['TStarClusterDescription']=='nan') ]

merged[(merged['final_match']==0)][['TStarClusterToken','TStarClusterDescription']]

incorrect_tstarc = merged[(merged['final_match']==0)]

incorrect_tstarc.to_csv('C:/Users/<USER>/Documents/OCR/data csv/invoice/incorrect_tstarc.csv')

1-(2821-1003)/len(merged)

len(merged)

1-2821/len(merged)