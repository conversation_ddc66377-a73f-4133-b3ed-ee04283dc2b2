{"cells": [{"cell_type": "code", "execution_count": null, "id": "8e9694cf", "metadata": {}, "outputs": [], "source": ["import json\n", "import requests\n", "\n", "# === Load environment variables from environment.json ===\n", "with open('environment.json') as f:\n", "    env_data = json.load(f)\n", "\n", "# Convert variables to a dict\n", "env_vars = {item['key']: item['value'] for item in env_data['values']}\n", "vethub_url = env_vars['vethubURL']\n", "\n", "# === Step 1: Get ConversationID ===\n", "conversation_url = f\"{vethub_url}/api/claim/NewConversationId\"\n", "response = requests.get(conversation_url)\n", "response.raise_for_status()\n", "conversation_id = response.json().get('conversationId') or response.text\n", "\n", "print(f\"[+] ConversationID: {conversation_id}\")\n", "\n", "# === Step 2: Get AttachmentID ===\n", "attachment_url = f\"{vethub_url}/api/claim/{conversation_id}/Attachment/NewConversationId\"\n", "response = requests.get(attachment_url)\n", "response.raise_for_status()\n", "attachment_id = response.json().get('attachmentId') or response.text\n", "\n", "print(f\"[+] AttachmentID: {attachment_id}\")\n", "\n", "# === Step 3: Submit Clinical Attachment ===\n", "# Replace this with actual payload and headers from collection.json if available\n", "clinical_attachment_url = f\"{vethub_url}/api/claim/{conversation_id}/Attachment\"\n", "clinical_payload = {\n", "    \"attachmentId\": attachment_id,\n", "    \"data\": \"BASE64_ENCODED_DATA\",  # Replace with actual data\n", "    \"type\": \"clinical\"\n", "}\n", "headers = {\n", "    \"Content-Type\": \"application/json\"\n", "}\n", "\n", "response = requests.post(clinical_attachment_url, json=clinical_payload, headers=headers)\n", "response.raise_for_status()\n", "print(\"[+] Clinical attachment submitted\")\n", "\n", "# === Step 4: Submit Claim ===\n", "claim_url = f\"{vethub_url}/api/claim/{conversation_id}\"\n", "claim_payload = {\n", "    \"claim\": {\n", "        \"conversationId\": conversation_id,\n", "        \"details\": \"CLAIM_DETAILS\"  # Replace with actual claim data\n", "    }\n", "}\n", "\n", "response = requests.post(claim_url, json=claim_payload, headers=headers)\n", "response.raise_for_status()\n", "print(\"[+] <PERSON><PERSON><PERSON> submitted successfully\")\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}