import json
import requests

# === Load environment variables from environment.json ===
with open('environment.json') as f:
    env_data = json.load(f)

# Convert variables to a dict
env_vars = {item['key']: item['value'] for item in env_data['values']}
vethub_url = env_vars['vethubURL']

# === Step 1: Get ConversationID ===
conversation_url = f"{vethub_url}/api/claim/NewConversationId"
response = requests.get(conversation_url)
response.raise_for_status()
conversation_id = response.json().get('conversationId') or response.text

print(f"[+] ConversationID: {conversation_id}")

# === Step 2: Get AttachmentID ===
attachment_url = f"{vethub_url}/api/claim/{conversation_id}/Attachment/NewConversationId"
response = requests.get(attachment_url)
response.raise_for_status()
attachment_id = response.json().get('attachmentId') or response.text

print(f"[+] AttachmentID: {attachment_id}")

# === Step 3: Submit Clinical Attachment ===
# Replace this with actual payload and headers from collection.json if available
clinical_attachment_url = f"{vethub_url}/api/claim/{conversation_id}/Attachment"
clinical_payload = {
    "attachmentId": attachment_id,
    "data": "BASE64_ENCODED_DATA",  # Replace with actual data
    "type": "clinical"
}
headers = {
    "Content-Type": "application/json"
}

response = requests.post(clinical_attachment_url, json=clinical_payload, headers=headers)
response.raise_for_status()
print("[+] Clinical attachment submitted")

# === Step 4: Submit Claim ===
claim_url = f"{vethub_url}/api/claim/{conversation_id}"
claim_payload = {
    "claim": {
        "conversationId": conversation_id,
        "details": "CLAIM_DETAILS"  # Replace with actual claim data
    }
}

response = requests.post(claim_url, json=claim_payload, headers=headers)
response.raise_for_status()
print("[+] Claim submitted successfully")
